import {
  type BaseDataResp,
  type BaseIDReq,
  type BaseListReq,
  type BaseResp,
} from '#/api/model/baseModel';
import { requestClient } from '#/api/request';

import {
  type CreateWarehouseReq,
  type CreateWarehouseResp,
  type GetWarehouseDetailReq,
  type GetWarehouseDetailResp,
  type GetWarehouseListReq,
  type GetWarehouseListResp,
  type UpdateWarehouseReq,
  type UpdateWarehouseResp,
  type WarehouseInfo,
} from './model/warehouseModel';

enum Api {
  CreateWarehouse = '/warehouse/create',
  DeleteWarehouse = '/warehouse/delete',
  GetWarehouseDetail = '/warehouse/detail',
  GetWarehouseList = '/warehouse/list',
  UpdateWarehouse = '/warehouse/update',
}

/**
 * @description: Get warehouse list
 */
export const getWarehouseList = (params: GetWarehouseListReq) => {
  return requestClient.get<BaseDataResp<GetWarehouseListResp>>(Api.GetWarehouseList, {
    params,
  });
};

/**
 * @description: Get warehouse detail
 */
export const getWarehouseDetail = (params: GetWarehouseDetailReq) => {
  return requestClient.get<BaseDataResp<GetWarehouseDetailResp>>(
    `${Api.GetWarehouseDetail}/${params.id}`,
  );
};

/**
 * @description: Create a new warehouse
 */
export const createWarehouse = (params: CreateWarehouseReq) => {
  return requestClient.post<BaseDataResp<CreateWarehouseResp>>(Api.CreateWarehouse, params);
};

/**
 * @description: Update warehouse
 */
export const updateWarehouse = (params: UpdateWarehouseReq) => {
  return requestClient.put<UpdateWarehouseResp>(Api.UpdateWarehouse, params);
};

/**
 * @description: Delete warehouse
 */
export const deleteWarehouse = (params: BaseIDReq) => {
  return requestClient.delete<BaseResp>(Api.DeleteWarehouse, { data: params });
};

/**
 * @description: Batch delete warehouses
 */
export const batchDeleteWarehouse = (params: { ids: number[] }) => {
  return requestClient.delete<BaseResp>(Api.DeleteWarehouse, { data: params });
};
