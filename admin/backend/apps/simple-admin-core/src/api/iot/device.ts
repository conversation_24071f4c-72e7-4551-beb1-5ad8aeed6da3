import { requestClient } from '#/api/request';
import { type BaseDataResp, type BaseResp } from '../model/baseModel';
import {
  type ImportDeviceReq,
  type ImportDeviceResp,
  type ManufacturerCreateReq,
  type ManufacturerCreateResp,
  type ManufacturerListReq,
  type ManufacturerListResp,
} from './model/deviceModel';

enum Api {
  ImportDevice = '/iot-api/device/import',
  CreateManufacturer = '/iot-api/device/manufacturer/create',
  GetManufacturerList = '/iot-api/device/manufacturer/list',
}

/**
 * @description: 导入设备
 */
export const importDevice = (params: ImportDeviceReq) => {
  return requestClient.post<BaseResp>(Api.ImportDevice, params);
};

/**
 * @description: 创建/修改设备厂商
 */
export const createManufacturer = (params: ManufacturerCreateReq) => {
  return requestClient.post<BaseResp>(Api.CreateManufacturer, params);
};

/**
 * @description: 获取设备厂商列表
 */
export const getManufacturerList = (params: ManufacturerListReq) => {
  return requestClient.post<BaseDataResp<ManufacturerListResp>>(Api.GetManufacturerList, params);
};
