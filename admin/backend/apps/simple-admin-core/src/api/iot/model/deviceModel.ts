import {
  type BaseDataResp,
  type BaseListResp,
  type BaseResp,
  type BaseListReq,

} from '../../model/baseModel';

export interface FileInfo {
  name: string;
  size: number;
  type: string;
  url: string;
}

export interface ImportDeviceReq {
  file: FileInfo;
  channel: number;
  cost: number;
  tags?: string[];
  cover: boolean;
  deviceType: string;
  model: string;
  serialPrefix?: string;
}

export type ImportDeviceResp = BaseResp;

export interface ManufacturerInfo {
  id: number;
  name: string;
  contactPerson: string;
  contactPhone: string;
  contactEmail: string;
  address: string;
  bankAccount: string;
  bankName: string;
  createdAt: number;
  updatedAt: number;
}

export interface ManufacturerCreateReq {
  id?: number;
  name: string;
  contactPerson?: string;
  contactPhone?: string;
  contactEmail?: string;
  address?: string;
  bankAccount?: string;
  bankName?: string;
}

export type ManufacturerCreateResp = BaseResp;

export interface ManufacturerListReq {
  page: BaseListReq;
  name?: string;
  contactPerson?: string;
  contactPhone?: string;
}
export type ManufacturerListResp = BaseListResp<ManufacturerInfo>;
