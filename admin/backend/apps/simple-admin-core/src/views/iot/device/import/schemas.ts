import type { VxeGridProps } from '#/adapter/vxe-table';
import { h } from 'vue';
import { type VbenFormProps } from '@vben/common-ui';
import { Button, Tag, Tooltip } from 'ant-design-vue';
import { z } from '#/adapter/form';
import { getManufacturerList } from '#/api/iot/device';
import dayjs from 'dayjs';
import { router } from '#/router';
import { VbenIcon } from '../../../../../../../packages/@core/ui-kit/shadcn-ui/src/components';

export const tableColumns: VxeGridProps = {
  columns: [
    {
      title: '创建时间',
      field: 'createdAt',
      width: 180,
      formatter: ({ cellValue }) => {
        return dayjs(cellValue * 1000).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '文件名称',
      field: 'fileName',
      slots: {
        default: ({ row }) => {
          try {
            const extData = JSON.parse(row.extData || '{}');
            const fileName = extData.file.name || '未知文件名';
            return [
              h(
                'div',
                {
                  class: 'flex items-center justify-center gap-2',
                },
                [
                  fileName,
                  h(Tooltip, { title: '下载文件' }, () =>
                    h(VbenIcon, {
                      class: 'cursor-pointer',
                      icon: 'material-symbols:download',
                      size: 16,
                      onClick: () => {
                        window.open(extData.file.url, '_blank');
                      },
                    }),
                  ),
                ],
              ),
            ];
          } catch (error) {
            return '文件名解析错误';
          }
        },
      },
    },
    {
      title: '设备类型',
      field: 'deviceType',
      width: 120,
      slots: {
        default: ({ row }) => {
          try {
            const extData = JSON.parse(row.extData || '{}');
            return extData.deviceType || '未知设备类型';
          } catch (error) {
            return '数据解析错误';
          }
        },
      },
    },
    {
      title: '设备型号',
      field: 'model',
      width: 140,
      slots: {
        default: ({ row }) => {
          try {
            const extData = JSON.parse(row.extData || '{}');
            return extData.model || '未知型号';
          } catch (error) {
            return '数据解析错误';
          }
        },
      },
    },
    {
      title: '总记录数',
      field: 'totalRecords',
      width: 100,
      slots: {
        default: ({ row }) =>
          h(
            Button,
            {
              type: 'link',
              onClick: () => {
                router.push({
                  path: '/iot/task/log',
                  query: {
                    taskId: row.id,
                    status: 0,
                  },
                });
              },
            },
            () => row.totalRecords,
          ),
      },
    },
    {
      title: '成功记录',
      field: 'successRecords',
      width: 100,
      slots: {
        default: ({ row }) =>
          h(
            Button,
            {
              type: 'link',
              style: { color: 'green' },
              onClick: () => {
                router.push({
                  path: '/iot/task/log',
                  query: {
                    taskId: row.id,
                    status: 1,
                  },
                });
              },
            },
            () => row.successRecords,
          ),
      },
    },
    {
      title: '失败记录',
      field: 'failedRecords',
      width: 100,
      slots: {
        default: ({ row }) =>
          h(
            Button,
            {
              type: 'link',
              style: { color: 'red' },
              onClick: () => {
                router.push({
                  path: '/iot/task/log',
                  query: {
                    taskId: row.id,
                    status: 2,
                  },
                });
              },
            },
            () => row.failedRecords,
          ),
      },
    },
    {
      title: '操作人',
      field: 'operatorId',
      width: 220,
    },
    {
      title: '状态',
      field: 'status',
      width: 100,
      slots: {
        default: ({ row }) =>
          h(
            Tag,
            {
              color:
                row.status === 1
                  ? 'default'
                  : row.status === 2
                    ? 'processing'
                    : row.status === 3
                      ? 'success'
                      : 'error',
            },
            () => {
              switch (row.status) {
                case 1:
                  return '待处理';
                case 2:
                  return '处理中';
                case 3:
                  return '已完成';
                case 4:
                  return '失败';
                default:
                  return '未知';
              }
            },
          ),
      },
    },
    {
      title: '文字状态',
      field: 'errorMessage',
      align: 'center',
      width: 200,
      slots: {
        default: ({ row }) =>
          h(
            Tooltip,
            {
              placement: 'top',
              title: row.errorMessage,
            },
            () =>
              h(
                'div',
                {
                  style: {
                    width: '200px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  },
                },
                row.errorMessage,
              ),
          ),
      },
    },
    {
      title: '操作',
      field: 'action',
      width: 100,
      slots: {
        default: ({ row }) =>
          h(
            Button,
            {
              type: 'link',
              onClick: () => {
                router.push({
                  path: '/iot/task/log',
                  query: {
                    taskId: row.id,
                    status: 0,
                  },
                });
              },
            },
            () => '查看详情',
          ),
      },
    },
  ],
};

export const searchFormSchemas: VbenFormProps = {
  schema: [
    {
      fieldName: 'status',
      label: '状态',
      component: 'Select',
      rules: 'required',
      componentProps: {
        options: [
          { label: '全部', value: 0 },
          { label: '待处理', value: 1 },
          { label: '处理中', value: 2 },
          { label: '已完成', value: 3 },
          { label: '失败', value: 4 },
        ],
      },
      defaultValue: 0,
    },
  ],
};

export const importFormSchemas: VbenFormProps = {
  schema: [
    {
      fieldName: 'manufacturer',
      label: '设备厂商',
      component: 'ApiSelect',
      componentProps: {
        api: getManufacturerList,
        params: {
          page: {
            page: 1,
            pageSize: 1000,
          },
        },
        labelField: 'name',
        valueField: 'id',
      },
      rules: 'required',
    },
    {
      fieldName: 'deviceType',
      label: '设备类型',
      component: 'Select',
      componentProps: {
        options: [
          { label: 'eSIM设备', value: 'esim' },
          { label: 'WiFi设备', value: 'wifi' },
          { label: '4G设备', value: '4g' },
          { label: '5G设备', value: '5g' },
          { label: '其他', value: 'other' },
        ],
        placeholder: '请选择设备类型',
      },
      rules: 'required',
    },
    {
      fieldName: 'model',
      label: '设备型号',
      component: 'Input',
      componentProps: {
        placeholder: '请输入设备型号',
      },
      rules: 'required',
    },
    {
      fieldName: 'serialPrefix',
      label: '序列号前缀',
      component: 'Input',
      componentProps: {
        placeholder: '请输入序列号前缀（可选）',
      },
      help: '用于批量生成设备序列号，如果不填写则使用文件中的序列号',
    },
    {
      fieldName: 'costPrice',
      label: '设备成本单价',
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        placeholder: '请输入设备成本单价',
      },
      rules: z
        .number()
        .min(0, '设备成本单价不能小于0')
        .and(z.number().max(100000, '设备成本单价不能大于100000')),
      defaultValue: 0,
      suffix: '¥',
      help: '设备成本单价，用于成本核算',
    },
    {
      fieldName: 'tags',
      label: '设备标签',
      component: 'Select',
      componentProps: {
        mode: 'tags',
        placeholder: '请输入标签，可多选',
        tokenSeparators: [',', '，', ' '],
      },
      help: '输入标签后按回车或逗号分隔，可添加多个标签，用于设备分类管理',
    },
    {
      fieldName: 'cover',
      label: '覆盖已存在设备',
      component: 'Switch',
      componentProps: {
        checkedChildren: '是',
        unCheckedChildren: '否',
      },
      defaultValue: false,
      help: '如果设备号已存在，是否覆盖原有数据',
    },
    {
      fieldName: 'file',
      label: '导入文件',
      component: 'UploadFile',
      componentProps: {
        maxCount: 1,
        maxSize: 10 * 1024 * 1024,
        multiple: false,
        accept: ['xlsx', 'xls', 'csv'],
      },
      rules: z
        .object({
          url: z.string(),
          name: z.string(),
        })
        .refine(
          (val) => {
            return val && val.url;
          },
          {
            message: '请选择导入文件',
          },
        ),
      help: '支持Excel(.xlsx, .xls)和CSV格式文件，文件大小不超过10MB',
    },
  ],
};
