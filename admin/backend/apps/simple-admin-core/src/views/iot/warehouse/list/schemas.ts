import type { VxeGridProps } from '#/adapter/vxe-table';

import { h } from 'vue';

import { type VbenFormProps } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Switch, Tag } from 'ant-design-vue';

import { z } from '#/adapter/form';
import { updateWarehouse } from '#/api/warehouse/warehouse';

export const tableColumns: VxeGridProps = {
  columns: [
    {
      type: 'checkbox',
      width: 60,
    },
    {
      title: '云仓编码',
      field: 'warehouseCode',
      width: 120,
    },
    {
      title: '云仓名称',
      field: 'warehouseName',
      width: 150,
    },
    {
      title: '公司名称',
      field: 'companyName',
      width: 150,
    },
    {
      title: '联系人',
      field: 'contactPerson',
      width: 100,
    },
    {
      title: '联系电话',
      field: 'contactPhone',
      width: 120,
    },
    {
      title: '联系邮箱',
      field: 'contactEmail',
      width: 150,
    },
    {
      title: '省份',
      field: 'province',
      width: 80,
    },
    {
      title: '城市',
      field: 'city',
      width: 80,
    },
    {
      title: '状态',
      field: 'status',
      width: 80,
      slots: {
        default: (e) =>
          h(Switch, {
            checked: e.row.status === 1,
            onClick: () => {
              const newStatus = e.row.status === 1 ? 2 : 1;
              updateWarehouse({ id: e.row.id, status: newStatus }).then(() => {
                e.row.status = newStatus;
              });
            },
          }),
      },
    },
    {
      title: $t('common.createTime'),
      field: 'createdAt',
      formatter: 'formatDateTime',
      width: 150,
    },
  ],
};

export const searchFormSchemas: VbenFormProps = {
  schema: [
    {
      fieldName: 'keyword',
      label: '搜索关键词',
      component: 'Input',
      componentProps: {
        placeholder: '请输入云仓名称或编码',
      },
    },
    {
      fieldName: 'status',
      label: '状态',
      component: 'Select',
      componentProps: {
        options: [
          { label: '全部', value: 0 },
          { label: '正常', value: 1 },
          { label: '停用', value: 2 },
        ],
        placeholder: '请选择状态',
      },
    },
  ],
};

export const dataFormSchemas: VbenFormProps = {
  schema: [
    {
      fieldName: 'id',
      label: 'ID',
      component: 'Input',
      dependencies: {
        show: false,
        triggerFields: ['id'],
      },
    },
    {
      fieldName: 'warehouseCode',
      label: '云仓编码',
      component: 'Input',
      rules: z.string().min(1, '云仓编码不能为空').max(50, '云仓编码不能超过50个字符'),
      componentProps: {
        placeholder: '请输入云仓编码',
      },
    },
    {
      fieldName: 'warehouseName',
      label: '云仓名称',
      component: 'Input',
      rules: z.string().min(1, '云仓名称不能为空').max(100, '云仓名称不能超过100个字符'),
      componentProps: {
        placeholder: '请输入云仓名称',
      },
    },
    {
      fieldName: 'companyName',
      label: '公司名称',
      component: 'Input',
      rules: z.string().min(1, '公司名称不能为空').max(100, '公司名称不能超过100个字符'),
      componentProps: {
        placeholder: '请输入公司名称',
      },
    },
    {
      fieldName: 'contactPerson',
      label: '联系人',
      component: 'Input',
      rules: z.string().min(1, '联系人不能为空').max(50, '联系人不能超过50个字符'),
      componentProps: {
        placeholder: '请输入联系人姓名',
      },
    },
    {
      fieldName: 'contactPhone',
      label: '联系电话',
      component: 'Input',
      rules: z.string().min(1, '联系电话不能为空').max(20, '联系电话不能超过20个字符'),
      componentProps: {
        placeholder: '请输入联系电话',
      },
    },
    {
      fieldName: 'contactEmail',
      label: '联系邮箱',
      component: 'Input',
      rules: z.string().email('请输入正确的邮箱格式').optional(),
      componentProps: {
        placeholder: '请输入联系邮箱（可选）',
      },
    },
    {
      fieldName: 'address',
      label: '详细地址',
      component: 'Input',
      rules: z.string().min(1, '详细地址不能为空').max(200, '详细地址不能超过200个字符'),
      componentProps: {
        placeholder: '请输入详细地址',
      },
    },
    {
      fieldName: 'province',
      label: '省份',
      component: 'Input',
      rules: z.string().min(1, '省份不能为空').max(50, '省份不能超过50个字符'),
      componentProps: {
        placeholder: '请输入省份',
      },
    },
    {
      fieldName: 'city',
      label: '城市',
      component: 'Input',
      rules: z.string().min(1, '城市不能为空').max(50, '城市不能超过50个字符'),
      componentProps: {
        placeholder: '请输入城市',
      },
    },
    {
      fieldName: 'district',
      label: '区县',
      component: 'Input',
      rules: z.string().max(50, '区县不能超过50个字符').optional(),
      componentProps: {
        placeholder: '请输入区县（可选）',
      },
    },
    {
      fieldName: 'apiEndpoint',
      label: 'API接口地址',
      component: 'Input',
      rules: z.string().min(1, 'API接口地址不能为空').max(200, 'API接口地址不能超过200个字符'),
      componentProps: {
        placeholder: '请输入奇门API接口地址',
      },
    },
    {
      fieldName: 'appKey',
      label: 'APP Key',
      component: 'Input',
      rules: z.string().min(1, 'APP Key不能为空').max(100, 'APP Key不能超过100个字符'),
      componentProps: {
        placeholder: '请输入奇门API应用密钥',
      },
    },
    {
      fieldName: 'appSecret',
      label: 'APP Secret',
      component: 'Input',
      rules: z.string().min(1, 'APP Secret不能为空').max(100, 'APP Secret不能超过100个字符'),
      componentProps: {
        placeholder: '请输入奇门API应用密钥',
        type: 'password',
      },
    },
    {
      fieldName: 'customerId',
      label: '客户ID',
      component: 'Input',
      rules: z.string().min(1, '客户ID不能为空').max(100, '客户ID不能超过100个字符'),
      componentProps: {
        placeholder: '请输入奇门API客户ID',
      },
    },
    {
      fieldName: 'status',
      label: '状态',
      component: 'RadioButtonGroup',
      defaultValue: 1,
      componentProps: {
        options: [
          { label: '正常', value: 1 },
          { label: '停用', value: 2 },
        ],
      },
    },
  ],
};
