<template>
  <Page auto-content-height>
    <Grid>
    </Grid>
  </Page>
</template>

<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';
import { h, ref } from 'vue';
import { Page, type VbenFormProps } from '@vben/common-ui';
import { Button } from 'ant-design-vue';
import { ReloadOutlined } from '@ant-design/icons-vue';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { searchFormSchemas, tableColumns } from './schemas';
import { getCardList } from '#/api/iot/card';
import type { CardInfo } from '#/api/iot/model/cardModel';

defineOptions({
  name: 'CardList',
});

const formOptions: VbenFormProps = {
  schema: [...(searchFormSchemas.schema as any)],
  collapsed: false,
  showCollapseButton: true,
  submitOnEnter: false,
  wrapperClass: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4',
};

const gridOptions: VxeGridProps<CardInfo> = {
  columns: tableColumns.columns,
  height: 'auto',
  keepSource: true,
  stripe: true,
  toolbarConfig: {
    custom: true,
    export: false,
    import: false,
    refresh: true,
    zoom: true,
  },
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page },formData) => {
        const res = await getCardList({
          page: {
            page: page.currentPage,
            pageSize: page.pageSize,
          },
          ...formData,
        });
        return res.data;
      },
    },
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
  formOptions,
});

function handleRefresh() {
  gridApi.reload();
}
</script> 
